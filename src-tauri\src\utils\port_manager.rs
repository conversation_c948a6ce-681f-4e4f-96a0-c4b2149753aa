use rand::Rng;
use std::net::{SocketAddr, TcpListener};
use std::sync::{Arc, Mutex};

/// 端口管理器，负责端口检测和随机端口分配
pub struct PortManager {
    default_port: u16,
    current_port: Arc<Mutex<Option<u16>>>,
}

impl PortManager {
    /// 创建新的端口管理器
    fn new(default_port: u16) -> Self {
        Self {
            default_port,
            current_port: Arc::new(Mutex::new(None)),
        }
    }

    /// 检查指定端口是否可用
    fn is_port_available(&self, port: u16) -> bool {
        match TcpListener::bind(SocketAddr::from(([127, 0, 0, 1], port))) {
            Ok(_) => true,
            Err(_) => false,
        }
    }

    /// 获取一个可用的端口，优先使用默认端口，如果不可用则随机选择
    pub fn get_available_port(&self) -> Result<u16, String> {
        // 首先尝试默认端口
        if self.is_port_available(self.default_port) {
            let mut current = self
                .current_port
                .lock()
                .map_err(|e| format!("Failed to lock port: {}", e))?;
            *current = Some(self.default_port);
            println!("Using default port: {}", self.default_port);
            return Ok(self.default_port);
        }

        println!(
            "Default port {} is not available, searching for random port...",
            self.default_port
        );

        // 如果默认端口不可用，尝试随机端口
        let mut rng = rand::thread_rng();
        let mut attempts = 0;
        const MAX_ATTEMPTS: u32 = 100;

        while attempts < MAX_ATTEMPTS {
            // 在 49152-65535 范围内选择随机端口（动态端口范围）
            let random_port = rng.gen_range(49152..=65535);

            if self.is_port_available(random_port) {
                let mut current = self
                    .current_port
                    .lock()
                    .map_err(|e| format!("Failed to lock port: {}", e))?;
                *current = Some(random_port);
                println!("Using random port: {}", random_port);
                return Ok(random_port);
            }

            attempts += 1;
        }

        Err(format!(
            "Failed to find available port after {} attempts",
            MAX_ATTEMPTS
        ))
    }

    /// 获取当前使用的端口
    pub fn get_current_port(&self) -> Option<u16> {
        self.current_port.lock().ok().and_then(|port| *port)
    }

    /// 重置端口管理器状态
    pub fn reset(&self) {
        if let Ok(mut current) = self.current_port.lock() {
            *current = None;
        }
    }
}

/// 全局端口管理器实例
lazy_static::lazy_static! {
    pub static ref CORE_PORT_MANAGER: PortManager = PortManager::new(50721);
}

/// 获取核心服务的可用端口
pub fn get_core_port() -> Result<u16, String> {
    let core_port_opt = CORE_PORT_MANAGER.get_current_port();
    if let Some(core_port) = core_port_opt {
        return Ok(core_port);
    }
    CORE_PORT_MANAGER.get_available_port()
}
