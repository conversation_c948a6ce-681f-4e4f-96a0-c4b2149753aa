import React, { useState, useEffect, useRef, useLayoutEffect } from "react";
import { useSelector } from "react-redux";
import { selectInputValue } from "../../slices/inputValueSlice";
import FileIcon from "../FileIcon";
import { Data, DataType } from "../MainAnwser";
import { highlight } from "../../api/aiverything";
import { getCurrentWindow } from "@tauri-apps/api/window";

// 自定义 hook 来处理高亮数据的延迟加载
const useHighlightObserver = (callback) => {
  const observer = useRef<IntersectionObserver | null>(null);
  const callbackRef = useRef(callback);

  // 更新回调引用，避免重新创建 observer
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    observer.current = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          callbackRef.current(entry.target);
        }
      });
    });

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []); // 移除 callback 依赖，避免重新创建

  const observe = (element) => {
    if (element && observer.current) {
      // 延迟观察
      setTimeout(() => {
        if (observer.current) {
          observer.current.observe(element);
        }
      }, 100);
    }
  };

  const unobserve = (element) => {
    if (element && observer.current) {
      observer.current.unobserve(element);
    }
  };

  const reconnect = () => {
    if (observer.current) {
      observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            callbackRef.current(entry.target);
          }
        });
      });
    }
  };

  return { observe, unobserve, reconnect };
};

// 高亮显示组件
const HighlightedText: React.FC<{ text: string; className?: string }> = ({
  text,
  className,
}) => {
  if (!text) return null;

  // 解析包含<highlight></highlight>标签的文本
  const parts = text.split(/(<highlight>.*?<\/highlight>)/g);

  return (
    <span className={className}>
      {parts.map((part, index) => {
        if (part.startsWith("<highlight>") && part.endsWith("</highlight>")) {
          // 移除标签，只保留内容
          const content = part.slice(11, -12); // 移除<highlight>和</highlight>
          return (
            <span
              key={index}
              className="bg-yellow-200 dark:bg-yellow-600 px-0.5 rounded text-black dark:text-white font-semibold"
            >
              {content}
            </span>
          );
        }
        return <span key={index}>{part}</span>;
      })}
    </span>
  );
};

const CompactFileListItem: React.FC<{
  file: Data;
  asyncHighlightData?: { fileName?: string; parentPath?: string };
}> = ({ file, asyncHighlightData }) => {
  // 优先使用异步加载的高亮数据
  const highlightFileName =
    asyncHighlightData?.fileName || file.highlightFileName;
  const highlightPath = asyncHighlightData?.parentPath || file.highlightPath;

  return (
    <div className="flex items-center gap-2">
      <div className="w-5 h-5 flex-shrink-0">
        <FileIcon
          filepath={file.path}
          target="Compact"
          isUwp={file.type === DataType.UwpApp}
        />
      </div>
      <div className="flex-grow min-w-0">
        <div className="text-sm font-medium truncate dark:text-gray-200">
          {highlightFileName ? (
            <HighlightedText text={highlightFileName} />
          ) : (
            file.name
          )}
        </div>
        <div className="text-xs text-gray-500 truncate dark:text-gray-400">
          {highlightPath ? (
            <HighlightedText text={highlightPath} className="break-all" />
          ) : (
            file.path
          )}
        </div>
      </div>
    </div>
  );
};

const CompactFileList = ({ data, onSelect, onItemClick, searchUuid }) => {
  const [selectedFile, setSelectedFile] = useState<Data>();
  const [selectedIndex, setSelectedIndex] = useState({ section: 0, file: 0 });
  const [lastAction, setLastAction] = useState("keyboard");
  const inputValue = useSelector(selectInputValue);
  const selectedFileRef = useRef(null);
  const containerRef = useRef(null);

  // 添加状态来存储异步加载的高亮数据
  const [asyncHighlightData, setAsyncHighlightData] = useState<{
    [key: string]: { fileName?: string; parentPath?: string };
  }>({});

  // 使用 useRef 来跟踪已经请求过的文件
  const requestedFilesRef = useRef<Set<string>>(new Set());

  // 高亮数据加载函数
  const loadHighlightForFile = async (element: HTMLElement) => {
    const filePath = element.getAttribute("data-file-path");
    const fileName = element.getAttribute("data-file-name");
    const parentPath = element.getAttribute("data-parent-path");

    if (!filePath || !fileName || !parentPath || !searchUuid) return;

    // 如果已经请求过这个文件，跳过
    if (requestedFilesRef.current.has(filePath)) return;

    // 标记这个文件已经请求过
    requestedFilesRef.current.add(filePath);

    try {
      let fileNameProcessed = fileName;
      if (fileName?.endsWith(".lnk")) {
        fileNameProcessed = fileName.slice(0, -4);
      }
      const response = await highlight(
        fileNameProcessed,
        parentPath,
        searchUuid
      );
      const result = await response.json();

      if (result.status === 200 && result.data) {
        setAsyncHighlightData((prev) => ({
          ...prev,
          [filePath]: {
            fileName: result.data.fileName,
            parentPath: result.data.parentPath,
          },
        }));
      }
    } catch (error) {
      console.error("Failed to load highlight data:", error);
    }
  };

  // 使用自定义 hook 来观察文件项的可见性
  const {
    observe: observeHighlight,
    unobserve: unobserveHighlight,
    reconnect: reconnectObserver,
  } = useHighlightObserver(loadHighlightForFile);

  // 为每个文件项创建 ref 来观察可见性
  const fileItemRefs = useRef<Map<string, HTMLElement>>(new Map());

  const setFileItemRef = (filePath: string, element: HTMLElement | null) => {
    if (element) {
      fileItemRefs.current.set(filePath, element);
      // 对所有需要高亮的文件设置观察者（不管是否已有高亮数据）
      if (data) {
        const file = data
          .flatMap((section) => section?.data || [])
          .find((f) => f?.path === filePath);

        if (
          file &&
          file.path &&
          file.type !== DataType.SearchOnWeb &&
          file.type !== DataType.UwpApp
        ) {
          // 总是设置观察者，让 IntersectionObserver 决定是否需要加载
          observeHighlight(element);
        }
      }
    } else {
      fileItemRefs.current.delete(filePath);
    }
  };

  // 计算父目录路径的辅助函数
  const getParentPath = (filePath: string) => {
    const lastBackslash = filePath.lastIndexOf("\\");
    const lastForwardslash = filePath.lastIndexOf("/");
    const lastSeparatorIndex = Math.max(lastBackslash, lastForwardslash);
    return lastSeparatorIndex > 0
      ? filePath.substring(0, lastSeparatorIndex)
      : filePath;
  };

  // 当数据变化时，重新设置观察者
  useEffect(() => {
    // 清理旧的观察者和引用
    fileItemRefs.current.clear();
  }, [data]);

  // 当搜索任务变化时，清除缓存的高亮数据和请求记录
  useEffect(() => {
    setAsyncHighlightData({});
    requestedFilesRef.current.clear();
    fileItemRefs.current.clear();
  }, [searchUuid]);

  // 监听窗口焦点变化，当窗口重新获得焦点时重新加载高亮数据
  useEffect(() => {
    let unlisten: (() => void) | null = null;

    const setupWindowFocusListener = async () => {
      const currentWindow = getCurrentWindow();

      // 监听窗口焦点变化事件
      unlisten = await currentWindow.onFocusChanged(({ payload: focused }) => {
        if (focused && searchUuid && data?.length > 0) {
          console.log(
            "CompactFileList: Window focused, checking for highlight data to reload..."
          );

          // 窗口重新获得焦点时，重新连接 IntersectionObserver 并重新触发可见文件的高亮加载
          setTimeout(() => {
            console.log("CompactFileList: Reconnecting observer and reloading highlights...");

            // 重新连接 IntersectionObserver，确保滚动监听正常工作
            reconnectObserver();

            // 遍历当前所有的文件项，重新设置观察和加载高亮数据
            fileItemRefs.current.forEach((element, filePath) => {
              if (element && data) {
                const file = data
                  .flatMap((section: any) => section?.data || [])
                  .find((f: any) => f?.path === filePath);

                // 对所有需要高亮的文件重新设置观察
                if (
                  file &&
                  file.path &&
                  file.type !== DataType.SearchOnWeb &&
                  file.type !== DataType.UwpApp
                ) {
                  console.log(`CompactFileList: Re-observing file: ${filePath}`);

                  // 重新设置 IntersectionObserver 监听
                  observeHighlight(element);

                  // 对还没有高亮数据的文件，如果在视口中则立即加载
                  if (
                    !file.highlightFileName &&
                    !file.highlightPath &&
                    !asyncHighlightData[filePath]
                  ) {
                    // 检查元素是否在视口中
                    const rect = element.getBoundingClientRect();
                    const isVisible =
                      rect.top >= 0 && rect.bottom <= window.innerHeight;

                    if (isVisible) {
                      console.log(
                        `CompactFileList: Immediately loading highlight for visible file: ${filePath}`
                      );
                      // 从请求记录中移除，允许重新请求
                      requestedFilesRef.current.delete(filePath);
                      // 重新触发高亮加载
                      loadHighlightForFile(element);
                    }
                  }
                }
              }
            });

            console.log(`CompactFileList: Re-observed ${fileItemRefs.current.size} file items`);
          }, 100); // 延迟100ms确保窗口完全显示
        }
      });
    };

    setupWindowFocusListener().catch((err) => {
      console.error("CompactFileList: 设置窗口焦点监听器失败:", err);
    });

    return () => {
      if (unlisten) {
        unlisten();
      }
    };
  }, [
    searchUuid,
    data,
    asyncHighlightData,
    loadHighlightForFile,
    observeHighlight,
  ]);

  useLayoutEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Alt") {
        event.preventDefault();
      }
      setLastAction("keyboard");
      if (event.key === "ArrowDown") {
        setSelectedIndex((prevIndex) => {
          const newIndex = { ...prevIndex };
          if (!data?.[newIndex.section]?.data) {
            return newIndex;
          }
          if (newIndex.file < data[newIndex.section].data.length - 1) {
            newIndex.file += 1;
          } else if (newIndex.section < data.length - 1) {
            if (data?.[newIndex.section + 1]?.data?.length > 0) {
              newIndex.section += 1;
              newIndex.file = 0;
            }
          }
          return newIndex;
        });
      } else if (event.key === "ArrowUp") {
        setSelectedIndex((prevIndex) => {
          const newIndex = { ...prevIndex };
          if (!data?.[newIndex.section]?.data) {
            return newIndex;
          }
          if (newIndex.file > 0) {
            newIndex.file -= 1;
          } else if (newIndex.section > 0) {
            if (data?.[newIndex.section - 1]?.data?.length > 0) {
              newIndex.section -= 1;
              newIndex.file = data[newIndex.section].data.length - 1;
            }
          }
          return newIndex;
        });
      }
    };

    // 只在数据为空或只有一项时重置选择
    if (!data || data.length <= 1) {
      setSelectedIndex({ section: 0, file: 0 });
    } else {
      // 检查当前选择的文件是否在新数据中存在
      const currentSelectedFile = selectedFile;
      if (currentSelectedFile) {
        let fileFound = false;
        data.forEach((section, sectionIndex) => {
          if (section?.data) {
            const fileIndex = section.data.findIndex(
              (item) => item?.path === currentSelectedFile.path
            );
            if (fileIndex !== -1) {
              fileFound = true;
              setSelectedIndex({ section: sectionIndex, file: fileIndex });
            }
          }
        });

        // 只有在当前选择的文件不存在于新数据中时才重置选择
        if (!fileFound) {
          if (data[0]?.dataType === DataType.SearchOnWeb && data.length > 1) {
            if (data[1]?.data?.length > 0) {
              setSelectedIndex({ section: 1, file: 0 });
            } else {
              let foundValidSection = false;
              for (let i = 0; i < data.length; i++) {
                if (data[i]?.data?.length > 0) {
                  setSelectedIndex({ section: i, file: 0 });
                  foundValidSection = true;
                  break;
                }
              }

              if (!foundValidSection) {
                setSelectedIndex({ section: 0, file: 0 });
              }
            }
          } else {
            setSelectedIndex({ section: 0, file: 0 });
          }
        }
      } else {
        // 如果没有选中的文件，则设置默认选择
        if (data[0]?.dataType === DataType.SearchOnWeb && data.length > 1) {
          if (data[1]?.data?.length > 0) {
            setSelectedIndex({ section: 1, file: 0 });
          } else {
            let foundValidSection = false;
            for (let i = 0; i < data.length; i++) {
              if (data[i]?.data?.length > 0) {
                setSelectedIndex({ section: i, file: 0 });
                foundValidSection = true;
                break;
              }
            }

            if (!foundValidSection) {
              setSelectedIndex({ section: 0, file: 0 });
            }
          }
        } else {
          setSelectedIndex({ section: 0, file: 0 });
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [data]);

  useEffect(() => {
    if (data?.length > 0) {
      const selectedFile =
        data?.[selectedIndex.section]?.data?.[selectedIndex.file];
      setSelectedFile(selectedFile);
      onSelect(selectedFile);
      if (selectedFileRef.current) {
        setTimeout(() => {
          if (selectedFileRef.current) {
            selectedFileRef.current.scrollIntoView({
              behavior: lastAction == "keyboard" ? "auto" : "smooth",
              block: "nearest",
            });
          }
        }, 0);
      }
    }
  }, [selectedIndex, onSelect, lastAction]);

  useEffect(() => {
    if (
      data?.length > 0 &&
      selectedIndex.section === 0 &&
      selectedIndex.file === 0
    ) {
      if (containerRef.current) {
        containerRef.current.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }
    }
  }, [data]);

  useEffect(() => {
    setSelectedIndex({ section: 0, file: 0 });
  }, [inputValue]);

  if (!data || data.length === 0 || !inputValue) {
    return null;
  }

  return (
    <ul
      ref={containerRef}
      className="w-full h-full p-2 overflow-y-auto dark:bg-gray-800"
      style={{
        background: "transparent",
      }}
    >
      {data.map(
        (section, sectionIndex) =>
          section && (
            <li key={sectionIndex} className="mb-2">
              <h3 className="text-xs font-medium text-gray-500 mb-1 px-1 dark:text-gray-400">
                {section.datatype}
              </h3>
              <ul className="list-none m-0 p-0">
                {section.data?.map(
                  (file: Data, fileIndex) =>
                    file && (
                      <li
                        key={file?.path || fileIndex}
                        ref={(element) => {
                          // 处理选中文件的 ref
                          if (selectedFile === file) {
                            selectedFileRef.current = element;
                          }
                          // 处理高亮观察的 ref
                          setFileItemRef(file.path, element);
                        }}
                        className={`py-1 px-2 rounded cursor-pointer ${
                          selectedFile === file
                            ? "bg-blue-100 dark:bg-gray-700"
                            : ""
                        }`}
                        onMouseMove={() => {
                          setLastAction("mouse");
                          setSelectedFile(file);
                          onSelect(file);
                          setSelectedIndex({
                            section: sectionIndex,
                            file: fileIndex,
                          });
                        }}
                        onClick={() => onItemClick(file)}
                        data-file-path={file.path}
                        data-file-name={file.name}
                        data-parent-path={getParentPath(file.path)}
                      >
                        <CompactFileListItem
                          file={file}
                          asyncHighlightData={asyncHighlightData[file.path]}
                        />
                      </li>
                    )
                )}
              </ul>
            </li>
          )
      )}
    </ul>
  );
};

export default CompactFileList;
